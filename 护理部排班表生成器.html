<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>护理部排班表生成器</title>
    <!-- 引入SheetJS库用于Excel文件操作 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .form-section {
            margin-bottom: 25px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .form-section h3 {
            margin-top: 0;
            color: #555;
        }
        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }
        .form-group {
            flex: 1;
            min-width: 150px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #666;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }
        .fixed-floor-select {
            width: 120px !important;
            flex-shrink: 0;
        }
        .fixed-night-input {
            width: 100px !important;
            flex-shrink: 0;
        }
        .employee-list {
            margin-top: 15px;
        }
        .employee-item {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            align-items: center;
        }
        .employee-item input:first-child {
            flex: 2;
            min-width: 120px;
        }
        .employee-item input:nth-child(2) {
            flex: 1;
            min-width: 80px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn:hover {
            opacity: 0.9;
        }
        .schedule-table {
            margin-top: 30px;
            overflow-x: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #333;
            padding: 8px;
            text-align: center;
            font-size: 12px;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .employee-name {
            background-color: #e9ecef;
            font-weight: bold;
            min-width: 80px;
        }
        .date-header {
            background-color: #d1ecf1;
            writing-mode: vertical-rl;
            text-orientation: mixed;
            min-width: 30px;
        }
        .schedule-cell {
            min-width: 50px;
            height: 40px;
            font-size: 11px;
            padding: 2px;
        }
        .shift-1f { background-color: #fff3cd; }
        .shift-2f { background-color: #d4edda; }
        .shift-3f { background-color: #cce5ff; }
        .shift-night { background-color: #f8d7da; }
        .shift-rest { background-color: #e2e3e5; }
        .notes {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            font-size: 12px;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>护理部排班表生成器</h1>

        <div class="form-section">
            <h3>基本信息</h3>
            <div class="form-row">
                <div class="form-group">
                    <label for="year">年份:</label>
                    <input type="number" id="year" value="2025" min="2020" max="2030">
                </div>
                <div class="form-group">
                    <label for="month">月份:</label>
                    <select id="month">
                        <option value="1">1月</option>
                        <option value="2">2月</option>
                        <option value="3">3月</option>
                        <option value="4">4月</option>
                        <option value="5" selected>5月</option>
                        <option value="6">6月</option>
                        <option value="7">7月</option>
                        <option value="8">8月</option>
                        <option value="9">9月</option>
                        <option value="10">10月</option>
                        <option value="11">11月</option>
                        <option value="12">12月</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="baseRestDays">本月基础休息天数:</label>
                    <input type="number" id="baseRestDays" value="4" min="0" max="10">
                </div>
            </div>
        </div>

        <div class="form-section">
            <h3>人员配置</h3>
            <div class="form-row">
                <div class="form-group">
                    <label for="floor1Count">一楼人数:</label>
                    <input type="number" id="floor1Count" value="2" min="1" max="10">
                </div>
                <div class="form-group">
                    <label for="floor2Count">二楼人数:</label>
                    <input type="number" id="floor2Count" value="2" min="1" max="10">
                </div>
                <div class="form-group">
                    <label for="floor3Count">三楼人数:</label>
                    <input type="number" id="floor3Count" value="2" min="1" max="10">
                </div>
                <div class="form-group">
                    <label for="nightCount">夜班人数:</label>
                    <input type="number" id="nightCount" value="1" min="1" max="5">
                </div>
                <div class="form-group">
                    <label for="consecutiveNightDays">连续夜班天数:</label>
                    <input type="number" id="consecutiveNightDays" value="3" min="1" max="10" title="每个员工连续上夜班的天数">
                </div>
            </div>
        </div>

        <div class="form-section">
            <h3>员工信息</h3>
            <div class="employee-list" id="employeeList">
                <!-- 员工列表将在这里动态生成 -->
            </div>
            <button class="btn btn-secondary" onclick="addEmployee()">添加员工</button>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button class="btn btn-primary" onclick="generateSchedule()">生成排班表</button>
            <div id="saveButtons" style="display: none; margin-top: 10px;">
                <button class="btn btn-success" onclick="saveAsHtml()">保存为HTML</button>
                <button class="btn btn-success" onclick="saveAsExcel()">保存为Excel</button>
            </div>
        </div>

        <div class="schedule-table" id="scheduleResult">
            <!-- 排班表将在这里显示 -->
        </div>
    </div>

    <script>
        // 初始化员工列表
        function initializeEmployees() {
            const employees = [
                { name: '韦美合', lastMonthLeave: 0, fixedFloor: '', fixedNightShifts: -1 },
                { name: '黄容昌', lastMonthLeave: 0, fixedFloor: '', fixedNightShifts: -1 },
                { name: '黄菊香', lastMonthLeave: 0, fixedFloor: '', fixedNightShifts: -1 },
                { name: '冉茂琼', lastMonthLeave: 0, fixedFloor: '', fixedNightShifts: -1 },
                { name: '李向明', lastMonthLeave: 0, fixedFloor: '', fixedNightShifts: -1 },
                { name: '李晋东', lastMonthLeave: 0, fixedFloor: '', fixedNightShifts: -1 },
                { name: '冉启会', lastMonthLeave: 0, fixedFloor: '', fixedNightShifts: -1 },
                { name: '徐红星', lastMonthLeave: 0, fixedFloor: '', fixedNightShifts: -1 }
            ];

            const employeeList = document.getElementById('employeeList');
            employeeList.innerHTML = '';

            employees.forEach((emp, index) => {
                addEmployeeRow(emp.name, emp.lastMonthLeave, emp.fixedFloor, emp.fixedNightShifts);
            });
        }

        function addEmployee() {
            addEmployeeRow('', 0);
        }

        function addEmployeeRow(name = '', lastMonthLeave = 0, fixedFloor = '', fixedNightShifts = -1) {
            const employeeList = document.getElementById('employeeList');
            const div = document.createElement('div');
            div.className = 'employee-item';
            div.innerHTML = `
                <input type="text" placeholder="员工姓名" value="${name}">
                <input type="number" placeholder="上月余假" value="${lastMonthLeave}" min="0" max="20">
                <select class="fixed-floor-select">
                    <option value="">不固定楼层</option>
                    <option value="一楼" ${fixedFloor === '一楼' ? 'selected' : ''}>固定一楼</option>
                    <option value="二楼" ${fixedFloor === '二楼' ? 'selected' : ''}>固定二楼</option>
                    <option value="三楼" ${fixedFloor === '三楼' ? 'selected' : ''}>固定三楼</option>
                </select>
                <input type="number" class="fixed-night-input" placeholder="固定夜班次数" value="${fixedNightShifts}" min="-1" max="31" title="设置-1为不固定，设置正数为固定夜班次数">
                <button class="btn btn-secondary" onclick="removeEmployee(this)">删除</button>
            `;
            employeeList.appendChild(div);
        }

        function removeEmployee(btn) {
            btn.parentElement.remove();
        }

        // 获取月份天数
        function getDaysInMonth(year, month) {
            return new Date(year, month, 0).getDate();
        }

        // 获取星期几
        function getWeekday(year, month, day) {
            const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
            const date = new Date(year, month - 1, day);
            return weekdays[date.getDay()];
        }

        // 生成排班表
        function generateSchedule() {
            const year = parseInt(document.getElementById('year').value);
            const month = parseInt(document.getElementById('month').value);
            const baseRestDays = parseInt(document.getElementById('baseRestDays').value);
            const floor1Count = parseInt(document.getElementById('floor1Count').value);
            const floor2Count = parseInt(document.getElementById('floor2Count').value);
            const floor3Count = parseInt(document.getElementById('floor3Count').value);
            const nightCount = parseInt(document.getElementById('nightCount').value);
            const consecutiveNightDays = parseInt(document.getElementById('consecutiveNightDays').value);

            // 获取员工信息
            const employees = [];
            const employeeItems = document.querySelectorAll('.employee-item');
            employeeItems.forEach(item => {
                const inputs = item.querySelectorAll('input');
                const select = item.querySelector('.fixed-floor-select');
                const name = inputs[0].value.trim();
                const lastMonthLeave = parseInt(inputs[1].value) || 0;
                const fixedFloor = select.value;
                const fixedNightShifts = parseInt(inputs[2].value) || -1;
                if (name) {
                    employees.push({ name, lastMonthLeave, fixedFloor, fixedNightShifts });
                }
            });

            if (employees.length === 0) {
                alert('请至少添加一名员工');
                return;
            }

            const daysInMonth = getDaysInMonth(year, month);
            const schedule = generateScheduleData(employees, daysInMonth, baseRestDays, floor1Count, floor2Count, floor3Count, nightCount, consecutiveNightDays);

            displaySchedule(year, month, daysInMonth, employees, schedule);
            document.getElementById('saveButtons').style.display = 'block';
        }

        // 排班算法核心
        function generateScheduleData(employees, daysInMonth, baseRestDays, floor1Count, floor2Count, floor3Count, nightCount, consecutiveNightDays) {
            const schedule = {};
            const employeeCount = employees.length;
            const totalDailyNeeded = floor1Count + floor2Count + floor3Count + nightCount;

            // 验证员工数量是否足够
            if (employeeCount < totalDailyNeeded) {
                alert(`员工总数(${employeeCount})不足以满足每日人员需求(${totalDailyNeeded})，请增加员工或减少人员配置要求。`);
                return { schedule: {}, employeeAccumulatedLeave: {} };
            }

            // 初始化每个员工的排班
            employees.forEach(emp => {
                schedule[emp.name] = new Array(daysInMonth).fill('');
            });

            // 计算每个员工的总休息天数（基础+余假，最多6天）
            const employeeRestDays = {};
            const employeeAccumulatedLeave = {};
            employees.forEach(emp => {
                const totalRest = baseRestDays + emp.lastMonthLeave;
                employeeRestDays[emp.name] = Math.min(totalRest, 6);
                employeeAccumulatedLeave[emp.name] = Math.max(0, totalRest - 6);
            });

            // 第一步：分配夜班（连续上班+夜班后必须休息）
            assignNightShifts(schedule, employees, daysInMonth, nightCount, consecutiveNightDays);

            // 第二步：安排剩余休息天数
            assignRestDays(schedule, employees, daysInMonth, employeeRestDays, employeeAccumulatedLeave, baseRestDays);

            // 第三步：分配楼层班次（包括填补空白）
            assignFloorShifts(schedule, employees, daysInMonth, floor1Count, floor2Count, floor3Count);

            // 第四步：填补所有空白位置
            fillEmptySlots(schedule, employees, daysInMonth);

            // 第五步：验证夜班人数
            validateNightShiftCoverage(schedule, employees, daysInMonth, nightCount);

            return { schedule, employeeAccumulatedLeave };
        }

        // 分配夜班 - 严格按照连续夜班天数和每日夜班人数要求
        function assignNightShifts(schedule, employees, daysInMonth, nightCount, consecutiveNightDays) {
            // 分别处理固定夜班次数和灵活夜班的员工
            const fixedNightEmployees = employees.filter(emp => emp.fixedNightShifts >= 0);
            const flexibleEmployees = employees.filter(emp => emp.fixedNightShifts < 0);

            // 先为固定夜班次数的员工分配夜班（连续分配且遵守每日夜班人数限制）
            assignFixedNightShifts(schedule, fixedNightEmployees, daysInMonth, nightCount, consecutiveNightDays);

            // 然后为灵活员工按连续天数规则分配夜班
            assignFlexibleNightShiftsWithConsecutive(schedule, flexibleEmployees, daysInMonth, nightCount, consecutiveNightDays);

            // 确保每天夜班人数严格等于设置值
            ensureExactNightShiftCount(schedule, employees, daysInMonth, nightCount);

            // 确保夜班后有休息
            ensureRestAfterNightShift(schedule, employees, daysInMonth);
        }

        // 为固定夜班次数的员工分配夜班 - 连续分配且遵守每日夜班人数限制
        function assignFixedNightShifts(schedule, fixedNightEmployees, daysInMonth, nightCount, consecutiveNightDays) {
            // 为每个固定夜班员工记录已分配的夜班次数
            const assignedCounts = {};
            fixedNightEmployees.forEach(emp => {
                assignedCounts[emp.name] = 0;
            });

            // 按紧急程度排序固定夜班员工
            const sortedEmployees = [...fixedNightEmployees].sort((a, b) => {
                // 按固定夜班次数从多到少排序，确保需求大的员工优先分配
                return b.fixedNightShifts - a.fixedNightShifts;
            });

            // 为每个固定夜班员工分配连续夜班
            sortedEmployees.forEach(emp => {
                let remainingNights = emp.fixedNightShifts;
                let startDay = 0;

                while (remainingNights > 0 && startDay < daysInMonth) {
                    // 找到可以开始连续夜班的位置
                    while (startDay < daysInMonth && schedule[emp.name][startDay] !== '') {
                        startDay++;
                    }

                    if (startDay >= daysInMonth) break;

                    // 计算这次连续夜班的天数
                    const consecutiveDaysThisRound = Math.min(consecutiveNightDays, remainingNights);

                    // 检查是否可以分配这段连续夜班
                    let canAssignConsecutive = true;
                    for (let checkDay = startDay; checkDay < startDay + consecutiveDaysThisRound && checkDay < daysInMonth; checkDay++) {
                        const currentNightCount = Object.keys(schedule).filter(empName => schedule[empName][checkDay] === '夜班').length;
                        if (currentNightCount >= nightCount) {
                            canAssignConsecutive = false;
                            break;
                        }
                    }

                    if (canAssignConsecutive) {
                        // 分配连续夜班
                        let assignedThisRound = 0;
                        for (let day = startDay; day < startDay + consecutiveDaysThisRound && day < daysInMonth; day++) {
                            const currentNightCount = Object.keys(schedule).filter(empName => schedule[empName][day] === '夜班').length;
                            if (currentNightCount < nightCount && schedule[emp.name][day] === '') {
                                schedule[emp.name][day] = '夜班';
                                assignedThisRound++;
                                assignedCounts[emp.name]++;
                            } else {
                                break; // 当天夜班人数已满或员工已有安排
                            }
                        }

                        remainingNights -= assignedThisRound;

                        // 夜班后安排休息
                        const restDay = startDay + assignedThisRound;
                        if (restDay < daysInMonth && schedule[emp.name][restDay] === '') {
                            schedule[emp.name][restDay] = '休';
                        }

                        startDay = restDay + 1; // 从休息日后开始下一轮
                    } else {
                        startDay++; // 无法在当前位置分配，尝试下一天
                    }
                }

                // 检查是否达到目标次数
                const assigned = assignedCounts[emp.name];
                const target = emp.fixedNightShifts;
                if (assigned < target) {
                    console.warn(`员工${emp.name}固定夜班次数不足: ${assigned}/${target}，可能需要调整设置或增加员工`);
                }
            });
        }

        // 为灵活员工按连续天数规则分配夜班
        function assignFlexibleNightShiftsWithConsecutive(schedule, flexibleEmployees, daysInMonth, nightCount, consecutiveNightDays) {
            if (flexibleEmployees.length === 0) return;

            let currentEmployee = 0;
            let day = 0;

            while (day < daysInMonth && currentEmployee < flexibleEmployees.length) {
                // 检查当天总夜班人数（包括固定夜班员工）
                const allEmployees = [...flexibleEmployees];
                // 需要获取所有员工来计算当天夜班总数
                const currentNightCount = Object.keys(schedule).filter(empName => schedule[empName][day] === '夜班').length;
                const needMore = nightCount - currentNightCount;

                if (needMore > 0) {
                    const emp = flexibleEmployees[currentEmployee];

                    // 检查该员工是否可以在这天开始连续夜班
                    if (schedule[emp.name][day] === '') {
                        // 检查连续夜班期间每天的夜班人数限制
                        let canAssignConsecutive = true;
                        let consecutiveDays = 0;
                        let checkDay = day;

                        // 预检查连续夜班期间是否会超过每日限制
                        while (consecutiveDays < consecutiveNightDays && checkDay < daysInMonth) {
                            const dayNightCount = Object.keys(schedule).filter(empName => schedule[empName][checkDay] === '夜班').length;
                            if (dayNightCount >= nightCount) {
                                canAssignConsecutive = false;
                                break;
                            }
                            consecutiveDays++;
                            checkDay++;
                        }

                        if (canAssignConsecutive) {
                            // 分配连续夜班
                            consecutiveDays = 0;
                            let currentDay = day;

                            while (consecutiveDays < consecutiveNightDays &&
                                   currentDay < daysInMonth &&
                                   schedule[emp.name][currentDay] === '') {
                                // 再次检查当天夜班人数限制
                                const dayNightCount = Object.keys(schedule).filter(empName => schedule[empName][currentDay] === '夜班').length;
                                if (dayNightCount < nightCount) {
                                    schedule[emp.name][currentDay] = '夜班';
                                    consecutiveDays++;
                                } else {
                                    break; // 当天夜班人数已满
                                }
                                currentDay++;
                            }

                            // 夜班后安排休息
                            if (currentDay < daysInMonth && schedule[emp.name][currentDay] === '') {
                                schedule[emp.name][currentDay] = '休';
                            }

                            // 切换到下一个员工
                            currentEmployee++;
                        } else {
                            // 无法分配连续夜班，跳到下一天
                            day++;
                        }
                    } else {
                        day++;
                    }
                } else {
                    day++;
                }

                // 如果所有员工都轮了一遍，重新开始
                if (currentEmployee >= flexibleEmployees.length) {
                    currentEmployee = 0;
                    day++; // 确保不会无限循环
                }
            }
        }

        // 确保每天夜班人数严格等于设置值
        function ensureExactNightShiftCount(schedule, employees, daysInMonth, nightCount) {
            for (let day = 0; day < daysInMonth; day++) {
                const currentNightCount = employees.filter(emp => schedule[emp.name][day] === '夜班').length;

                if (currentNightCount < nightCount) {
                    // 夜班人数不足，从其他班次调配
                    const needMore = nightCount - currentNightCount;
                    const available = employees.filter(emp =>
                        schedule[emp.name][day] !== '夜班' &&
                        schedule[emp.name][day] !== '休'
                    );

                    for (let i = 0; i < Math.min(needMore, available.length); i++) {
                        schedule[available[i].name][day] = '夜班';
                    }
                } else if (currentNightCount > nightCount) {
                    // 夜班人数过多，调配到其他班次
                    const tooMany = currentNightCount - nightCount;
                    const nightWorkers = employees.filter(emp => schedule[emp.name][day] === '夜班');

                    // 优先调配非固定夜班的员工
                    const flexibleNightWorkers = nightWorkers.filter(emp => emp.fixedNightShifts < 0);
                    const toReassign = flexibleNightWorkers.slice(0, tooMany);

                    toReassign.forEach(emp => {
                        schedule[emp.name][day] = ''; // 清空，后续会被填补
                    });
                }
            }
        }

        // 优化夜班连续性
        function optimizeNightShiftContinuity(schedule, employees, daysInMonth, nightCount) {
            // 尝试重新安排夜班，让同一员工连续上班
            const nightDaysPerEmployee = Math.floor(daysInMonth / employees.length);

            // 清除现有夜班安排
            employees.forEach(emp => {
                for (let day = 0; day < daysInMonth; day++) {
                    if (schedule[emp.name][day] === '夜班') {
                        schedule[emp.name][day] = '';
                    }
                }
            });

            // 重新按连续性分配夜班
            let currentEmployee = 0;
            let day = 0;

            while (day < daysInMonth) {
                // 为当前员工分配连续夜班
                let consecutiveDays = 0;
                while (consecutiveDays < nightDaysPerEmployee && day < daysInMonth) {
                    // 检查是否可以安排夜班（避免与已有安排冲突）
                    if (schedule[employees[currentEmployee].name][day] === '') {
                        schedule[employees[currentEmployee].name][day] = '夜班';
                        consecutiveDays++;
                    }
                    day++;
                }

                currentEmployee = (currentEmployee + 1) % employees.length;

                // 如果所有员工都轮了一遍，重新开始
                if (currentEmployee === 0 && day < daysInMonth) {
                    // 继续分配剩余天数
                }
            }

            // 确保每天都有足够的夜班人员
            for (let day = 0; day < daysInMonth; day++) {
                const nightToday = employees.filter(emp => schedule[emp.name][day] === '夜班').length;
                if (nightToday < nightCount) {
                    const needMore = nightCount - nightToday;
                    const available = employees.filter(emp => schedule[emp.name][day] === '');

                    for (let i = 0; i < Math.min(needMore, available.length); i++) {
                        schedule[available[i].name][day] = '夜班';
                    }
                }
            }
        }

        // 确保夜班后有休息
        function ensureRestAfterNightShift(schedule, employees, daysInMonth) {
            employees.forEach(emp => {
                for (let day = 0; day < daysInMonth - 1; day++) {
                    if (schedule[emp.name][day] === '夜班') {
                        // 检查第二天是否为空，如果是则安排休息
                        if (schedule[emp.name][day + 1] === '') {
                            schedule[emp.name][day + 1] = '休';
                        }
                    }
                }
            });
        }

        // 验证夜班覆盖情况
        function validateNightShiftCoverage(schedule, employees, daysInMonth, nightCount) {
            for (let day = 0; day < daysInMonth; day++) {
                const nightToday = employees.filter(emp => schedule[emp.name][day] === '夜班').length;
                if (nightToday < nightCount) {
                    console.warn(`第${day + 1}天夜班人数不足: ${nightToday}/${nightCount}`);

                    // 强制补充夜班人员
                    const needMore = nightCount - nightToday;
                    const available = employees.filter(emp =>
                        schedule[emp.name][day] !== '夜班' &&
                        schedule[emp.name][day] !== '休'
                    );

                    // 从楼层班次中调配人员到夜班
                    for (let i = 0; i < Math.min(needMore, available.length); i++) {
                        schedule[available[i].name][day] = '夜班';
                    }
                }
            }
        }

        // 分配楼层班次 - 优先考虑固定楼层
        function assignFloorShifts(schedule, employees, daysInMonth, floor1Count, floor2Count, floor3Count) {
            for (let day = 0; day < daysInMonth; day++) {
                const availableEmployees = employees.filter(emp =>
                    schedule[emp.name][day] === ''
                );

                // 分别收集固定楼层和非固定楼层的员工
                const fixedFloor1 = availableEmployees.filter(emp => emp.fixedFloor === '一楼');
                const fixedFloor2 = availableEmployees.filter(emp => emp.fixedFloor === '二楼');
                const fixedFloor3 = availableEmployees.filter(emp => emp.fixedFloor === '三楼');
                const flexibleEmployees = availableEmployees.filter(emp => !emp.fixedFloor);

                let assigned = 0;

                // 分配一楼 - 优先使用固定一楼的员工
                let floor1Assigned = 0;
                for (let i = 0; i < Math.min(floor1Count, fixedFloor1.length); i++) {
                    schedule[fixedFloor1[i].name][day] = '一楼';
                    floor1Assigned++;
                    assigned++;
                }
                // 如果固定员工不够，从灵活员工中补充
                for (let i = 0; i < floor1Count - floor1Assigned && assigned < availableEmployees.length; i++) {
                    const flexIndex = (day + i) % flexibleEmployees.length;
                    if (flexIndex < flexibleEmployees.length && schedule[flexibleEmployees[flexIndex].name][day] === '') {
                        schedule[flexibleEmployees[flexIndex].name][day] = '一楼';
                        assigned++;
                    }
                }

                // 分配二楼 - 优先使用固定二楼的员工
                let floor2Assigned = 0;
                for (let i = 0; i < Math.min(floor2Count, fixedFloor2.length); i++) {
                    schedule[fixedFloor2[i].name][day] = '二楼';
                    floor2Assigned++;
                    assigned++;
                }
                // 如果固定员工不够，从灵活员工中补充
                const remainingFlexible = flexibleEmployees.filter(emp => schedule[emp.name][day] === '');
                for (let i = 0; i < floor2Count - floor2Assigned && i < remainingFlexible.length; i++) {
                    schedule[remainingFlexible[i].name][day] = '二楼';
                    assigned++;
                }

                // 分配三楼 - 优先使用固定三楼的员工
                let floor3Assigned = 0;
                for (let i = 0; i < Math.min(floor3Count, fixedFloor3.length); i++) {
                    schedule[fixedFloor3[i].name][day] = '三楼';
                    floor3Assigned++;
                    assigned++;
                }
                // 如果固定员工不够，从灵活员工中补充
                const finalFlexible = flexibleEmployees.filter(emp => schedule[emp.name][day] === '');
                for (let i = 0; i < floor3Count - floor3Assigned && i < finalFlexible.length; i++) {
                    schedule[finalFlexible[i].name][day] = '三楼';
                    assigned++;
                }
            }
        }

        // 分配剩余休息天数 - 智能分配，优先满足基础休息天数
        function assignRestDays(schedule, employees, daysInMonth, employeeRestDays, employeeAccumulatedLeave, baseRestDays) {
            // 为每个员工创建休息需求列表
            const restNeeds = employees.map(emp => {
                const currentRestDays = schedule[emp.name].filter(shift => shift === '休').length;
                const totalNeeded = employeeRestDays[emp.name];
                const stillNeeded = Math.max(0, totalNeeded - currentRestDays);

                return {
                    employee: emp,
                    stillNeeded: stillNeeded,
                    priority: emp.lastMonthLeave > 0 ? 2 : 1 // 有余假的优先级更高
                };
            });

            // 按优先级和需求天数排序
            restNeeds.sort((a, b) => {
                if (a.priority !== b.priority) return b.priority - a.priority;
                return b.stillNeeded - a.stillNeeded;
            });

            // 分配休息天数 - 确保均匀分布且不连续休息超过两天
            distributeRestDaysEvenly(schedule, employees, daysInMonth, restNeeds);

            // 处理无法安排的休息天数
            restNeeds.forEach(item => {
                if (item.stillNeeded > 0) {
                    // 优先保证基础休息天数，余假部分累积到存假
                    const currentRestDays = schedule[item.employee.name].filter(shift => shift === '休').length;

                    if (currentRestDays < baseRestDays) {
                        // 基础休息天数不足，尝试强制安排
                        const needBase = baseRestDays - currentRestDays;
                        let arranged = 0;

                        for (let day = 0; day < daysInMonth && arranged < needBase; day++) {
                            if (schedule[item.employee.name][day] === '') {
                                const restCountToday = employees.filter(e => schedule[e.name][day] === '休').length;
                                // 检查连续休息限制和人数限制
                                if (restCountToday < 3 && canRestOnDay(schedule, item.employee.name, day, daysInMonth)) {
                                    schedule[item.employee.name][day] = '休';
                                    arranged++;
                                    item.stillNeeded--;
                                }
                            }
                        }
                    }

                    // 剩余天数累积到存假
                    if (item.stillNeeded > 0) {
                        employeeAccumulatedLeave[item.employee.name] += item.stillNeeded;
                    }
                }
            });
        }

        // 检查员工是否可以在指定日期休息（不能连续休息超过两天）
        function canRestOnDay(schedule, employeeName, day, daysInMonth) {
            // 检查前面的连续休息天数
            let consecutiveRestBefore = 0;
            for (let i = day - 1; i >= 0; i--) {
                if (schedule[employeeName][i] === '休') {
                    consecutiveRestBefore++;
                } else {
                    break;
                }
            }

            // 如果前面已经连续休息了2天，不能再休息
            if (consecutiveRestBefore >= 2) {
                return false;
            }

            // 如果前面休息了1天，检查后面一天是否也要休息
            if (consecutiveRestBefore === 1) {
                // 检查后面是否已经安排了休息
                if (day + 1 < daysInMonth && schedule[employeeName][day + 1] === '休') {
                    return false; // 这样会形成连续3天休息
                }
            }

            return true;
        }

        // 均匀分配休息天数
        function distributeRestDaysEvenly(schedule, employees, daysInMonth, restNeeds) {
            // 计算总的休息需求
            const totalRestNeeded = restNeeds.reduce((sum, item) => sum + item.stillNeeded, 0);

            // 创建时间段，将月份分成若干段
            const timeSegments = Math.ceil(daysInMonth / 7); // 按周分段
            const daysPerSegment = Math.ceil(daysInMonth / timeSegments);

            // 为每个时间段分配休息配额
            const restQuotaPerSegment = Math.ceil(totalRestNeeded / timeSegments);

            for (let segment = 0; segment < timeSegments; segment++) {
                const segmentStart = segment * daysPerSegment;
                const segmentEnd = Math.min((segment + 1) * daysPerSegment, daysInMonth);
                let segmentRestAssigned = 0;

                // 在当前时间段内分配休息
                for (let day = segmentStart; day < segmentEnd; day++) {
                    // 检查当天可以安排多少人休息
                    const currentRestCount = employees.filter(e => schedule[e.name][day] === '休').length;
                    const canRestToday = 2 - currentRestCount;

                    if (canRestToday > 0 && segmentRestAssigned < restQuotaPerSegment) {
                        // 找出当天可以休息的员工（考虑连续休息限制）
                        const availableForRest = restNeeds.filter(item => {
                            if (item.stillNeeded <= 0 || schedule[item.employee.name][day] !== '') {
                                return false;
                            }

                            // 检查连续休息限制
                            return canRestOnDay(schedule, item.employee.name, day, daysInMonth);
                        });

                        // 按优先级和需求分配休息
                        availableForRest.sort((a, b) => {
                            // 优先安排需求更多的员工
                            if (a.priority !== b.priority) return b.priority - a.priority;
                            return b.stillNeeded - a.stillNeeded;
                        });

                        const toAssign = Math.min(canRestToday, availableForRest.length, restQuotaPerSegment - segmentRestAssigned);
                        for (let i = 0; i < toAssign; i++) {
                            const item = availableForRest[i];
                            schedule[item.employee.name][day] = '休';
                            item.stillNeeded--;
                            segmentRestAssigned++;
                        }
                    }
                }
            }

            // 如果还有剩余的休息天数，进行第二轮分配
            const remainingRestNeeds = restNeeds.filter(item => item.stillNeeded > 0);
            if (remainingRestNeeds.length > 0) {
                for (let day = 0; day < daysInMonth; day++) {
                    const currentRestCount = employees.filter(e => schedule[e.name][day] === '休').length;
                    const canRestToday = 2 - currentRestCount;

                    if (canRestToday > 0) {
                        const availableForRest = remainingRestNeeds.filter(item => {
                            if (item.stillNeeded <= 0 || schedule[item.employee.name][day] !== '') {
                                return false;
                            }
                            return canRestOnDay(schedule, item.employee.name, day, daysInMonth);
                        });

                        for (let i = 0; i < Math.min(canRestToday, availableForRest.length); i++) {
                            const item = availableForRest[i];
                            schedule[item.employee.name][day] = '休';
                            item.stillNeeded--;
                        }
                    }
                }
            }
        }

        // 计算员工最长连续休息天数
        function getMaxConsecutiveRest(employeeSchedule) {
            let maxConsecutive = 0;
            let currentConsecutive = 0;

            for (let i = 0; i < employeeSchedule.length; i++) {
                if (employeeSchedule[i] === '休') {
                    currentConsecutive++;
                    maxConsecutive = Math.max(maxConsecutive, currentConsecutive);
                } else {
                    currentConsecutive = 0;
                }
            }

            return maxConsecutive;
        }

        // 验证每日夜班人数
        function validateDailyNightShift(schedule, employees, daysInMonth, requiredNightCount) {
            const issues = [];
            let hasIssues = false;

            for (let day = 0; day < daysInMonth; day++) {
                const nightToday = employees.filter(emp => schedule[emp.name][day] === '夜班').length;
                if (nightToday < requiredNightCount) {
                    issues.push(`第${day + 1}天夜班人数不足: ${nightToday}/${requiredNightCount}人`);
                    hasIssues = true;
                }
            }

            return { hasIssues, issues };
        }

        // 填补所有空白位置 - 优先考虑固定楼层
        function fillEmptySlots(schedule, employees, daysInMonth) {
            const floors = ['一楼', '二楼', '三楼'];

            for (let day = 0; day < daysInMonth; day++) {
                employees.forEach(emp => {
                    if (schedule[emp.name][day] === '') {
                        // 如果员工有固定楼层，优先分配到固定楼层
                        if (emp.fixedFloor) {
                            schedule[emp.name][day] = emp.fixedFloor;
                        } else {
                            // 没有固定楼层的员工按轮换方式分配
                            const floorIndex = (day + employees.indexOf(emp)) % floors.length;
                            schedule[emp.name][day] = floors[floorIndex];
                        }
                    }
                });
            }
        }

        // 显示排班表
        function displaySchedule(year, month, daysInMonth, employees, scheduleData) {
            const { schedule, employeeAccumulatedLeave } = scheduleData;
            const baseRestDays = parseInt(document.getElementById('baseRestDays').value);
            const monthNames = ['', '1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];

            let html = `
                <h2 style="text-align: center; margin-bottom: 20px;">
                    第一养老汕尾市城区颐养院 ${year} 年 ${monthNames[month]} 护理部排班表
                </h2>
                <table>
                    <thead>
                        <tr>
                            <th rowspan="2" class="employee-name">姓名<br>班次</th>
                            <th colspan="${daysInMonth}" style="text-align: center;">日期</th>
                        </tr>
                        <tr>`;

            // 生成日期头部
            for (let day = 1; day <= daysInMonth; day++) {
                const weekday = getWeekday(year, month, day);
                html += `<th class="date-header">${day}<br>${weekday}</th>`;
            }
            html += '</tr></thead><tbody>';

            // 生成员工排班行
            employees.forEach(emp => {
                html += `<tr><td class="employee-name">${emp.name}</td>`;
                for (let day = 0; day < daysInMonth; day++) {
                    const shift = schedule[emp.name][day];
                    let cellClass = 'schedule-cell';
                    let cellContent = shift;

                    switch(shift) {
                        case '一楼': cellClass += ' shift-1f'; cellContent = '一楼'; break;
                        case '二楼': cellClass += ' shift-2f'; cellContent = '二楼'; break;
                        case '三楼': cellClass += ' shift-3f'; cellContent = '三楼'; break;
                        case '夜班': cellClass += ' shift-night'; cellContent = '夜班'; break;
                        case '休': cellClass += ' shift-rest'; cellContent = '休'; break;
                        default: cellContent = '';
                    }

                    html += `<td class="${cellClass}">${cellContent}</td>`;
                }
                html += '</tr>';
            });

            html += '</tbody></table>';

            // 显示休息天数统计
            html += '<div style="margin: 20px 0; padding: 15px; background-color: #e9ecef; border-radius: 5px;">';
            html += '<h4>本月休息天数统计：</h4>';
            html += '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">';

            employees.forEach(emp => {
                const actualRestDays = schedule[emp.name].filter(shift => shift === '休').length;
                const nightDays = schedule[emp.name].filter(shift => shift === '夜班').length;
                const totalNeeded = Math.min(6, baseRestDays + emp.lastMonthLeave); // 基础休息天数+余假，最多6天
                const accumulated = employeeAccumulatedLeave[emp.name] || 0;
                const maxConsecutiveRest = getMaxConsecutiveRest(schedule[emp.name]);

                html += `<div style="padding: 8px; background: white; border-radius: 3px; border-left: 3px solid #007bff;">`;
                html += `<strong>${emp.name}</strong><br>`;
                if (emp.fixedFloor) {
                    html += `<span style="color: #6c757d;">固定: ${emp.fixedFloor}</span><br>`;
                }
                if (emp.fixedNightShifts >= 0) {
                    const nightStatus = nightDays === emp.fixedNightShifts ? '✅' : '⚠️';
                    html += `<span style="color: #6c757d;">固定夜班: ${emp.fixedNightShifts}天 ${nightStatus}</span><br>`;
                }
                html += `实际休息: ${actualRestDays}天<br>`;
                html += `应休息: ${totalNeeded}天<br>`;
                html += `夜班: ${nightDays}天<br>`;
                html += `最长连休: ${maxConsecutiveRest}天<br>`;
                if (accumulated > 0) {
                    html += `<span style="color: #ffc107;">存假: ${accumulated}天</span>`;
                }
                if (maxConsecutiveRest > 2) {
                    html += `<br><span style="color: #dc3545;">⚠️ 连休超限</span>`;
                }
                html += `</div>`;
            });

            html += '</div></div>';

            // 验证每日夜班人数
            const nightCount = parseInt(document.getElementById('nightCount').value);
            const nightShiftValidation = validateDailyNightShift(schedule, employees, daysInMonth, nightCount);
            if (nightShiftValidation.hasIssues) {
                html += '<div style="margin: 20px 0; padding: 10px; background-color: #f8d7da; border-left: 4px solid #dc3545;">';
                html += '<h4>⚠️ 夜班人数警告：</h4><ul>';
                nightShiftValidation.issues.forEach(issue => {
                    html += `<li>${issue}</li>`;
                });
                html += '</ul></div>';
            } else {
                html += '<div style="margin: 20px 0; padding: 10px; background-color: #d4edda; border-left: 4px solid #28a745;">';
                html += '<h4>✅ 夜班人数验证通过：每天都有足够的夜班人员</h4>';
                html += '</div>';
            }

            // 显示累积存假信息
            const hasAccumulatedLeave = Object.values(employeeAccumulatedLeave).some(leave => leave > 0);
            if (hasAccumulatedLeave) {
                html += '<div style="margin: 20px 0; padding: 10px; background-color: #fff3cd; border-left: 4px solid #ffc107;">';
                html += '<h4>本月累积存假：</h4><ul>';
                employees.forEach(emp => {
                    if (employeeAccumulatedLeave[emp.name] > 0) {
                        html += `<li>${emp.name}: ${employeeAccumulatedLeave[emp.name]}天</li>`;
                    }
                });
                html += '</ul></div>';
            }

            // 添加说明
            html += `
                <div class="notes">
                    <h4>说明：</h4>
                    <p><strong>1、</strong>每天保证本区域老人"三短 六洁"身上无异味。</p>
                    <p><strong>2、</strong>护理员休息前一天保证本区域老人"三短"，其他区域按分配帮休息区域打扫卫生和给老人洗澡。</p>
                    <p><strong>3、三短：</strong>头发短（保持头发清洁且不过长，避免影响患者的日常活动和卫生）、胡须短（修剪胡须，保持面部清洁，减少细菌滋生的机会）、指（趾）甲短（修剪指（趾）甲，避免过长导致的不适和可能的自我伤害）</p>
                    <p><strong>4、六洁：</strong></p>
                    <ul>
                        <li>口腔洁：保持口腔清洁，预防口腔感染；</li>
                        <li>头发洁：保持头发清洁，避免头皮屑和污垢积累；</li>
                        <li>手足洁：保持手部和足部的清洁，减少细菌滋生；</li>
                        <li>会阴洁：保持会阴部位的清洁，预防感染；</li>
                        <li>肛门洁：保持肛门部位的清洁，避免粪便残留；</li>
                        <li>皮肤洁：保持全身皮肤的清洁，预防压疮和其他皮肤问题。</li>
                    </ul>
                    <p><strong>5、</strong>零褥疮：通过勤翻身、勤擦洗等措施，预防褥疮的发生；零烫伤：注意使用热水等热源，避免烫伤；零坠床：采取适当的防护措施，防止患者坠床。</p>
                    <p><strong>6、</strong>二值人员保持24小时电话畅通，出现意外情况请立马拨打二值工作人员电话。</p>
                    <p><strong>7、</strong>值班人员联系方式：邱秀莲 13632642630   朱毅怡 13672827591   蔡玉燕 17325785764   黄菊香 13144492276</p>
                    <p><strong>8、</strong>护理员早班工作时间为早上7：00-19：00，晚班工作时间19：00-第二天早上7：00。</p>
                    <p><strong>9、</strong>早班加深标记为一值，12：00-14：00要在二楼护理站值班，检查服务对象居室、各功能室、服务对象精神状况以及睡眠服务质量是否正常，不可以回到自己房间休息。</p>
                </div>
            `;

            document.getElementById('scheduleResult').innerHTML = html;
        }

        // 保存为HTML文件
        function saveAsHtml() {
            const year = document.getElementById('year').value;
            const month = document.getElementById('month').value;
            const monthNames = ['', '1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];

            const scheduleContent = document.getElementById('scheduleResult').innerHTML;
            const fullContent = `
                <!DOCTYPE html>
                <html lang="zh-CN">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>护理部排班表 - ${year}年${monthNames[month]}</title>
                    <style>
                        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; }
                        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                        th, td { border: 1px solid #333; padding: 8px; text-align: center; font-size: 12px; }
                        th { background-color: #f8f9fa; font-weight: bold; }
                        .employee-name { background-color: #e9ecef; font-weight: bold; min-width: 80px; }
                        .date-header { background-color: #d1ecf1; writing-mode: vertical-rl; text-orientation: mixed; min-width: 30px; }
                        .schedule-cell { min-width: 50px; height: 40px; font-size: 11px; padding: 2px; }
                        .shift-1f { background-color: #fff3cd; }
                        .shift-2f { background-color: #d4edda; }
                        .shift-3f { background-color: #cce5ff; }
                        .shift-night { background-color: #f8d7da; }
                        .shift-rest { background-color: #e2e3e5; }
                        .notes { margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-left: 4px solid #007bff; font-size: 12px; line-height: 1.6; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    ${scheduleContent}
                </body>
                </html>
            `;

            const blob = new Blob([fullContent], { type: 'text/html;charset=utf-8' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `护理部排班表_${year}年${monthNames[month]}.html`;
            link.click();
            URL.revokeObjectURL(link.href);
        }

        // 保存为Excel文件
        function saveAsExcel() {
            const year = document.getElementById('year').value;
            const month = document.getElementById('month').value;
            const monthNames = ['', '1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];

            // 获取当前排班数据
            const employees = [];
            const employeeItems = document.querySelectorAll('.employee-item');
            employeeItems.forEach(item => {
                const inputs = item.querySelectorAll('input');
                const select = item.querySelector('.fixed-floor-select');
                const name = inputs[0].value.trim();
                const lastMonthLeave = parseInt(inputs[1].value) || 0;
                const fixedFloor = select.value;
                const fixedNightShifts = parseInt(inputs[2].value) || -1;
                if (name) {
                    employees.push({ name, lastMonthLeave, fixedFloor, fixedNightShifts });
                }
            });

            if (employees.length === 0) {
                alert('请先生成排班表');
                return;
            }

            const daysInMonth = getDaysInMonth(year, month);
            const baseRestDays = parseInt(document.getElementById('baseRestDays').value);
            const floor1Count = parseInt(document.getElementById('floor1Count').value);
            const floor2Count = parseInt(document.getElementById('floor2Count').value);
            const floor3Count = parseInt(document.getElementById('floor3Count').value);
            const nightCount = parseInt(document.getElementById('nightCount').value);
            const consecutiveNightDays = parseInt(document.getElementById('consecutiveNightDays').value);

            const scheduleData = generateScheduleData(employees, daysInMonth, baseRestDays, floor1Count, floor2Count, floor3Count, nightCount, consecutiveNightDays);
            const { schedule } = scheduleData;

            // 创建Excel工作簿
            const wb = XLSX.utils.book_new();

            // 准备数据
            const data = [];

            // 标题行
            data.push([`第一养老汕尾市城区颐养院 ${year} 年 ${monthNames[month]} 护理部排班表`]);
            data.push([]); // 空行

            // 表头
            const headerRow1 = ['姓名\\班次'];
            const headerRow2 = [''];

            for (let day = 1; day <= daysInMonth; day++) {
                const weekday = getWeekday(year, month, day);
                headerRow1.push(`${day}日`);
                headerRow2.push(weekday);
            }

            data.push(headerRow1);
            data.push(headerRow2);

            // 员工排班数据
            employees.forEach(emp => {
                const row = [emp.name];
                for (let day = 0; day < daysInMonth; day++) {
                    const shift = schedule[emp.name][day];
                    let cellContent = '';
                    switch(shift) {
                        case '一楼': cellContent = '一楼'; break;
                        case '二楼': cellContent = '二楼'; break;
                        case '三楼': cellContent = '三楼'; break;
                        case '夜班': cellContent = '夜班'; break;
                        case '休': cellContent = '休'; break;
                        default: cellContent = shift || '';
                    }
                    row.push(cellContent);
                }
                data.push(row);
            });

            // 添加说明
            data.push([]); // 空行
            data.push(['说明：']);
            data.push(['1、每天保证本区域老人"三短 六洁"身上无异味。']);
            data.push(['2、护理员休息前一天保证本区域老人"三短"，其他区域按分配帮休息区域打扫卫生和给老人洗澡。']);
            data.push(['3、三短：头发短（保持头发清洁且不过长，避免影响患者的日常活动和卫生）、胡须短（修剪胡须，保持面部清洁，减少细菌滋生的机会）、指（趾）甲短（修剪指（趾）甲，避免过长导致的不适和可能的自我伤害）']);
            data.push(['4、六洁：']);
            data.push(['   口腔洁：保持口腔清洁，预防口腔感染；']);
            data.push(['   头发洁：保持头发清洁，避免头皮屑和污垢积累；']);
            data.push(['   手足洁：保持手部和足部的清洁，减少细菌滋生；']);
            data.push(['   会阴洁：保持会阴部位的清洁，预防感染；']);
            data.push(['   肛门洁：保持肛门部位的清洁，避免粪便残留；']);
            data.push(['   皮肤洁：保持全身皮肤的清洁，预防压疮和其他皮肤问题。']);
            data.push(['5、零褥疮：通过勤翻身、勤擦洗等措施，预防褥疮的发生；零烫伤：注意使用热水等热源，避免烫伤；零坠床：采取适当的防护措施，防止患者坠床。']);
            data.push(['6、二值人员保持24小时电话畅通，出现意外情况请立马拨打二值工作人员电话。']);
            data.push(['7、值班人员联系方式：邱秀莲 13632642630   朱毅怡 13672827591   蔡玉燕 17325785764   黄菊香 13144492276']);
            data.push(['8、护理员早班工作时间为早上7：00-19：00，晚班工作时间19：00-第二天早上7：00。']);
            data.push(['9、早班加深标记为一值，12：00-14：00要在二楼护理站值班，检查服务对象居室、各功能室、服务对象精神状况以及睡眠服务质量是否正常，不可以回到自己房间休息。']);

            // 创建工作表
            const ws = XLSX.utils.aoa_to_sheet(data);

            // 设置列宽
            const colWidths = [{ wch: 12 }]; // 姓名列
            for (let i = 0; i < daysInMonth; i++) {
                colWidths.push({ wch: 6 }); // 日期列，增加宽度以适应完整班次名称
            }
            ws['!cols'] = colWidths;

            // 合并标题单元格
            ws['!merges'] = [
                { s: { r: 0, c: 0 }, e: { r: 0, c: daysInMonth } } // 标题行合并
            ];

            // 添加工作表到工作簿
            XLSX.utils.book_append_sheet(wb, ws, '排班表');

            // 保存文件
            XLSX.writeFile(wb, `护理部排班表_${year}年${monthNames[month]}.xlsx`);
        }

        // 页面加载时初始化
        window.onload = function() {
            initializeEmployees();
        };
    </script>
</body>
</html>
